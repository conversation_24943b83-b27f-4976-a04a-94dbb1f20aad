# 需求文档

## 介绍

本功能旨在为家长提供一个自动化工具，用于从国家中小学智慧平台（https://basic.smartedu.cn/）下载电子教材和教学视频资源。该工具将能够解析平台的下载逻辑，处理M3U8视频流，并将其合并为MP4格式，方便家长为孩子准备学习资源。

## 需求

### 需求 1 - 课程资源筛选界面

**用户故事：** 作为家长，我希望通过与官网一致的界面筛选课程资源，以便快速找到孩子需要的学习材料。

#### 验收标准

1. 当系统启动时，系统应从 https://basic.smartedu.cn/syncClassroom 获取最新的筛选条件数据
2. 当用户选择学段时，系统应根据所选学段动态加载对应的年级选项
3. 当用户选择年级时，系统应根据所选学段和年级的组合动态加载对应的学科选项
4. 当用户选择学科时，系统应根据学段、年级、学科的组合动态加载对应的版本选项
5. 当用户选择版本时，系统应根据学段、年级、学科、版本的组合动态加载对应的册次选项
6. 当任一上级筛选条件变更时，系统应清空并重新加载所有下级选项，确保数据联动的准确性
7. 当所有筛选条件选择完成时，系统应以与官网相似的卡片或列表形式展示课程资源
8. 如果官网筛选结构或联动逻辑发生变化，系统应能够适应并保持功能正常

### 需求 2 - 电子教材下载

**用户故事：** 作为家长，我希望能够下载电子教材，以便为孩子提供离线学习资源。

#### 验收标准

1. 当用户通过筛选界面选择特定教材时，系统应能够解析并下载对应的电子教材
2. 当下载完成时，系统应将教材保存为PDF或其他可读格式
3. 当下载过程中出现错误时，系统应提供清晰的错误信息和重试机制
4. 如果教材已存在，系统应检查是否有更新版本

### 需求 3 - 教学视频下载

**用户故事：** 作为家长，我希望能够下载教学视频，以便孩子可以离线观看学习内容。

#### 验收标准

1. 当用户选择特定学科的课程视频时，系统应能够解析M3U8播放列表
2. 当M3U8文件解析完成时，系统应下载所有视频片段并合并为单个MP4文件
3. 当视频下载过程中出现网络中断时，系统应支持断点续传功能
4. 当合并完成时，系统应验证视频文件的完整性和播放质量

### 需求 4 - 批量下载管理

**用户故事：** 作为家长，我希望能够批量下载多个资源，以便高效地为孩子准备整个学期的学习材料。

#### 验收标准

1. 当用户选择多个资源时，系统应支持批量下载队列管理
2. 当批量下载进行时，系统应显示每个任务的进度和状态
3. 当某个下载任务失败时，系统应继续处理其他任务并记录失败信息
4. 如果用户暂停批量下载，系统应能够保存当前状态并支持恢复

### 需求 5 - 资源组织和管理

**用户故事：** 作为家长，我希望下载的资源能够按照学科、年级、章节等方式有序组织，以便快速找到所需内容。

#### 验收标准

1. 当资源下载完成时，系统应按照学科、年级、章节的层次结构组织文件
2. 当创建文件夹结构时，系统应使用清晰的命名规范
3. 当资源重复时，系统应能够检测并避免重复下载
4. 如果用户指定自定义存储路径，系统应支持自定义文件组织结构

### 需求 6 - 下载进度和状态监控

**用户故事：** 作为家长，我希望能够实时查看下载进度和状态，以便了解任务完成情况。

#### 验收标准

1. 当下载任务开始时，系统应显示实时进度条和下载速度
2. 当多个任务并行执行时，系统应显示总体进度和各个任务的详细状态
3. 当下载完成时，系统应提供完成通知和下载统计信息
4. 如果下载过程中出现警告或错误，系统应在状态界面中清晰显示

### 需求 7 - 用户认证和访问控制

**用户故事：** 作为家长，我希望能够选择免登录或登录方式访问资源，以便根据需要获取不同权限的学习材料。

#### 验收标准

1. 当用户首次使用时，系统应支持免登录模式访问公开的教育资源
2. 当用户需要访问更多资源时，系统应提供登录功能支持用户账号认证
3. 当用户登录后，系统应能够访问需要认证的教材和课程资源
4. 当用户在免登录和登录状态之间切换时，系统应保持下载任务的连续性
5. 如果登录会话过期，系统应提示用户重新登录并支持自动重试下载任务
6. 当平台要求验证码或其他验证方式时，系统应提供相应的用户交互界面

### 需求 8 - 平台兼容性和稳定性

**用户故事：** 作为家长，我希望工具能够稳定运行并适应平台的变化，以确保长期可用性。

#### 验收标准

1. 当智慧平台更新其接口或结构时，系统应具备一定的适应能力
2. 当网络连接不稳定时，系统应实现智能重试机制
3. 当系统资源不足时，系统应优雅地处理并提供相应提示
4. 如果平台实施反爬虫措施，系统应实现合理的请求频率控制