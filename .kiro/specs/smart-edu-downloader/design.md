# 设计文档

## 概述

智慧教育下载器是一个基于 Node.js 的桌面应用程序，用于从国家中小学智慧平台下载教育资源。系统采用 Electron 框架构建用户界面，使用模块化架构设计，支持电子教材和教学视频的批量下载，并提供与官网一致的资源筛选体验。

参考 FlyEduDownloader 项目的实现思路，系统将重点解决 M3U8 视频流的解析和合并、平台 API 的逆向工程、以及稳定的下载管理机制。

## 架构

### 整体架构

```mermaid
graph TB
    A[用户界面层 - Electron Renderer] --> B[业务逻辑层 - Main Process]
    B --> C[API服务层]
    B --> D[下载管理层]
    B --> E[文件管理层]
    
    C --> F[智慧平台API客户端]
    C --> G[资源解析器]
    
    D --> H[下载队列管理器]
    D --> I[M3U8处理器]
    D --> J[视频合并器]
    
    E --> K[文件组织器]
    E --> L[存储管理器]
    
    F --> M[国家中小学智慧平台]
    I --> N[FFmpeg]
```

### 技术栈

- **前端框架**: Electron + React + TypeScript
- **后端运行时**: Node.js
- **HTTP客户端**: Axios
- **视频处理**: FFmpeg
- **文件处理**: fs-extra
- **状态管理**: Zustand
- **UI组件**: Ant Design
- **构建工具**: Webpack + Electron Builder

## 组件和接口

### 1. 用户界面组件

#### FilterPanel 筛选面板
```typescript
interface FilterPanelProps {
  onFilterChange: (filters: CourseFilters) => void;
  loading: boolean;
}

interface CourseFilters {
  stage: string;      // 学段
  grade: string;      // 年级
  subject: string;    // 学科
  version: string;    // 版本
  volume: string;     // 册次
}
```

#### LoginPanel 登录面板
```typescript
interface LoginPanelProps {
  onLogin: (credentials: LoginCredentials) => void;
  onGuestMode: () => void;
  loading: boolean;
  error?: string;
  captchaRequired?: boolean;
  captchaImage?: string;
}

interface LoginCredentials {
  username: string;
  password: string;
  captcha?: string;
  captchaToken?: string;
}
```

#### ResourceList 资源列表
```typescript
interface ResourceListProps {
  resources: CourseResource[];
  onDownload: (resource: CourseResource) => void;
  onBatchDownload: (resources: CourseResource[]) => void;
  userLoggedIn: boolean;
}

interface CourseResource {
  id: string;
  title: string;
  type: 'textbook' | 'video';
  url: string;
  metadata: ResourceMetadata;
  requiresAuth: boolean;
  accessLevel: 'public' | 'registered' | 'premium';
}
```

#### DownloadManager 下载管理器
```typescript
interface DownloadManagerProps {
  tasks: DownloadTask[];
  onPause: (taskId: string) => void;
  onResume: (taskId: string) => void;
  onCancel: (taskId: string) => void;
}
```

### 2. API服务层

#### SmartEduClient 智慧平台客户端
```typescript
class SmartEduClient {
  async login(username: string, password: string, captcha?: string): Promise<AuthResult>;
  async logout(): Promise<void>;
  async checkAuthStatus(): Promise<boolean>;
  async getFilterOptions(parentFilter?: Partial<CourseFilters>): Promise<FilterOptions>;
  async searchResources(filters: CourseFilters): Promise<CourseResource[]>;
  async getResourceDetail(resourceId: string): Promise<ResourceDetail>;
  async getVideoPlaylist(videoId: string): Promise<M3U8Playlist>;
  async getCaptcha(): Promise<CaptchaInfo>;
}
```

#### AuthManager 认证管理器
```typescript
class AuthManager {
  async loginWithCredentials(username: string, password: string): Promise<AuthResult>;
  async loginAsGuest(): Promise<void>;
  async refreshToken(): Promise<boolean>;
  async getAuthHeaders(): Promise<Record<string, string>>;
  isLoggedIn(): boolean;
  getCurrentUser(): UserInfo | null;
}
```

#### ResourceParser 资源解析器
```typescript
class ResourceParser {
  parseTextbookUrl(url: string): TextbookInfo;
  parseVideoUrl(url: string): VideoInfo;
  parseM3U8Playlist(content: string): M3U8Segment[];
}
```

### 3. 下载管理层

#### DownloadQueue 下载队列
```typescript
class DownloadQueue {
  addTask(task: DownloadTask): void;
  pauseTask(taskId: string): void;
  resumeTask(taskId: string): void;
  cancelTask(taskId: string): void;
  getTaskStatus(taskId: string): TaskStatus;
}
```

#### M3U8Downloader M3U8下载器
```typescript
class M3U8Downloader {
  async downloadPlaylist(playlist: M3U8Playlist, outputPath: string): Promise<void>;
  async mergeSegments(segments: string[], outputPath: string): Promise<void>;
  onProgress: (progress: DownloadProgress) => void;
}
```

### 4. 文件管理层

#### FileOrganizer 文件组织器
```typescript
class FileOrganizer {
  generatePath(resource: CourseResource): string;
  createDirectoryStructure(basePath: string, resource: CourseResource): void;
  checkFileExists(filePath: string): boolean;
  validateFileIntegrity(filePath: string): boolean;
}
```

## 数据模型

### 核心数据结构

```typescript
// 用户认证
interface AuthResult {
  success: boolean;
  token?: string;
  user?: UserInfo;
  error?: string;
  requiresCaptcha?: boolean;
  captchaImage?: string;
}

interface UserInfo {
  id: string;
  username: string;
  displayName: string;
  permissions: string[];
}

interface CaptchaInfo {
  image: string; // base64编码的验证码图片
  token: string; // 验证码token
}

// 下载任务
interface DownloadTask {
  id: string;
  resource: CourseResource;
  status: 'pending' | 'downloading' | 'paused' | 'completed' | 'failed';
  progress: number;
  speed: number;
  estimatedTime: number;
  error?: string;
  requiresAuth: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// 资源元数据
interface ResourceMetadata {
  stage: string;
  grade: string;
  subject: string;
  version: string;
  volume: string;
  chapter?: string;
  lesson?: string;
  fileSize?: number;
  duration?: number;
}

// M3U8播放列表
interface M3U8Playlist {
  baseUrl: string;
  segments: M3U8Segment[];
  totalDuration: number;
}

interface M3U8Segment {
  url: string;
  duration: number;
  sequence: number;
}

// 筛选选项
interface FilterOptions {
  stages: FilterOption[];
  grades: FilterOption[];
  subjects: FilterOption[];
  versions: FilterOption[];
  volumes: FilterOption[];
}

interface FilterOption {
  value: string;
  label: string;
  children?: FilterOption[];
}
```

## 错误处理

### 错误类型定义

```typescript
enum ErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  PARSE_ERROR = 'PARSE_ERROR',
  FILE_ERROR = 'FILE_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  API_ERROR = 'API_ERROR'
}

class DownloadError extends Error {
  constructor(
    public type: ErrorType,
    message: string,
    public details?: any
  ) {
    super(message);
  }
}
```

### 错误处理策略

1. **网络错误**: 实现指数退避重试机制，最多重试3次
2. **解析错误**: 记录详细错误信息，提供手动重试选项
3. **文件错误**: 检查磁盘空间和权限，提供解决建议
4. **API错误**: 根据错误码提供相应的用户提示

### 重试机制

```typescript
class RetryManager {
  async executeWithRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    backoffMs: number = 1000
  ): Promise<T>;
}
```

## 测试策略

### 单元测试

- **API客户端测试**: 模拟智慧平台API响应，测试数据解析逻辑
- **下载管理器测试**: 测试队列管理、任务状态转换、错误处理
- **文件组织器测试**: 测试路径生成、目录创建、文件验证
- **M3U8处理器测试**: 测试播放列表解析、片段下载、视频合并

### 集成测试

- **端到端下载流程**: 从资源筛选到文件下载完成的完整流程
- **批量下载测试**: 测试多任务并发下载的稳定性
- **错误恢复测试**: 测试网络中断、磁盘空间不足等异常情况的处理

### 性能测试

- **并发下载性能**: 测试不同并发数下的下载效率和系统资源占用
- **大文件处理**: 测试长视频文件的下载和合并性能
- **内存使用**: 监控长时间运行时的内存泄漏情况

### 测试工具

- **单元测试**: Jest + @testing-library/react
- **E2E测试**: Playwright
- **API模拟**: MSW (Mock Service Worker)
- **性能监控**: 自定义性能指标收集器

## 实现要点

### 1. 智慧平台API逆向工程

参考 FlyEduDownloader 的实现，需要分析以下关键接口：
- 筛选条件获取接口
- 资源搜索接口  
- 资源详情接口
- 视频播放地址获取接口

### 2. M3U8视频处理

- 解析M3U8播放列表格式
- 并发下载视频片段
- 使用FFmpeg合并片段为MP4
- 处理加密的HLS流

### 3. 反爬虫对策

- 实现合理的请求频率限制
- 模拟真实浏览器请求头
- 处理可能的验证码或登录要求
- 实现User-Agent轮换

### 4. 下载优化

- 支持断点续传
- 智能并发控制
- 网络状态自适应
- 磁盘空间监控